<?php
/**
 * Test script to create a member with expired membership for testing renewal functionality
 */

session_start();
require_once 'config/database.php';
require_once 'includes/auth.php';

// Simple authentication check
if (!isset($_SESSION['user_id']) && !isset($_SESSION['is_local_admin'])) {
    die('Please login first');
}

$db = Database::getInstance();

try {
    $db->beginTransaction();
    
    // Check if test member already exists
    $existingMember = $db->fetch("SELECT id FROM members WHERE email = '<EMAIL>'");
    
    if ($existingMember) {
        echo "<p>Test member already exists. Updating expiry date...</p>";
        
        // Update the member to have a membership expiring in 3 days
        $db->update('members', [
            'end_date' => date('Y-m-d', strtotime('+3 days')), // Expires in 3 days
            'status' => 'active'
        ], 'id = ?', [$existingMember['id']]);

        echo "<p>✅ Test member updated with membership expiring in 3 days.</p>";
    } else {
        // Get the first available plan
        $plan = $db->fetch("SELECT * FROM plans WHERE is_active = 1 ORDER BY id LIMIT 1");
        
        if (!$plan) {
            throw new Exception('No active plans found. Please create a plan first.');
        }
        
        // Generate unique member ID
        $memberIdPrefix = 'GYM';
        $lastMember = $db->fetch("SELECT member_id FROM members ORDER BY id DESC LIMIT 1");
        if ($lastMember) {
            $lastNumber = (int)substr($lastMember['member_id'], 3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        $memberId = $memberIdPrefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
        
        // Create test member with expired membership
        $memberData = [
            'member_id' => $memberId,
            'first_name' => 'Test',
            'last_name' => 'Renewal',
            'email' => '<EMAIL>',
            'phone' => '555-0123',
            'plan_id' => $plan['id'],
            'start_date' => date('Y-m-d', strtotime('-27 days')), // Started 27 days ago
            'end_date' => date('Y-m-d', strtotime('+3 days')), // Expires in 3 days
            'status' => 'active'
        ];
        
        $newMemberId = $db->insert('members', $memberData);
        
        // Create initial payment record
        $receiptNumber = 'TST' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        $paymentData = [
            'member_id' => $newMemberId,
            'plan_id' => $plan['id'],
            'amount' => $plan['price'],
            'payment_method' => 'cash',
            'receipt_number' => $receiptNumber,
            'payment_date' => date('Y-m-d', strtotime('-27 days')),
            'start_date' => date('Y-m-d', strtotime('-27 days')),
            'end_date' => date('Y-m-d', strtotime('+3 days')),
            'notes' => 'Test member initial payment',
            'processed_by' => Auth::isLocalAdmin() ? null : Auth::id()
        ];
        
        $db->insert('payments', $paymentData);
        
        echo "<p>✅ Test member created successfully!</p>";
        echo "<p><strong>Member ID:</strong> {$memberId}</p>";
        echo "<p><strong>Name:</strong> Test Renewal</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Plan:</strong> {$plan['name']}</p>";
        echo "<p><strong>Status:</strong> Active (expires in 3 days)</p>";
    }
    
    $db->commit();
    
    echo "<br><p><a href='pages/renewals.php' style='background: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Go to Renewals Page</a></p>";
    
} catch (Exception $e) {
    $db->rollback();
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Renewal Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        p { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Renewal Setup</h1>
    <p>This script creates a test member with an expired membership to test the renewal functionality.</p>
</body>
</html>
