<?php
/**
 * Test page for enhanced notifications and UI feedback
 */

session_start();
require_once 'config/database.php';
require_once 'includes/auth.php';

// Simple authentication check
if (!isset($_SESSION['user_id']) && !isset($_SESSION['is_local_admin'])) {
    die('Please login first');
}

// Handle test actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'success':
            Session::success('This is a success message! Everything worked perfectly.');
            break;
        case 'error':
            Session::error('This is an error message! Something went wrong.');
            break;
        case 'warning':
            Session::warning('This is a warning message! Please be careful.');
            break;
        case 'info':
            Session::info('This is an info message! Here\'s some useful information.');
            break;
    }
    
    header('Location: test_notifications.php');
    exit;
}

$pageTitle = 'Test Notifications';
include 'includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            <i class="fas fa-bell mr-2 text-blue-600"></i>
            Test Enhanced Notifications & UI
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Test the enhanced notification system and UI feedback</p>
    </div>

    <!-- Flash Message Tests -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Flash Messages</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Test the enhanced flash message system with improved animations and styling.</p>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="success">
                <?= Auth::csrfField() ?>
                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                    <i class="fas fa-check mr-2"></i>Success
                </button>
            </form>
            
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="error">
                <?= Auth::csrfField() ?>
                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                    <i class="fas fa-times mr-2"></i>Error
                </button>
            </form>
            
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="warning">
                <?= Auth::csrfField() ?>
                <button type="submit" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Warning
                </button>
            </form>
            
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="info">
                <?= Auth::csrfField() ?>
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                    <i class="fas fa-info mr-2"></i>Info
                </button>
            </form>
        </div>
    </div>

    <!-- Toast Notifications Tests -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Toast Notifications</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Test the enhanced toast notification system with animations and progress bars.</p>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button onclick="showToast('Operation completed successfully!', 'success')" 
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                <i class="fas fa-check mr-2"></i>Success Toast
            </button>
            
            <button onclick="showToast('Something went wrong. Please try again.', 'error')" 
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                <i class="fas fa-times mr-2"></i>Error Toast
            </button>
            
            <button onclick="showToast('Please review your settings before continuing.', 'warning')" 
                    class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                <i class="fas fa-exclamation-triangle mr-2"></i>Warning Toast
            </button>
            
            <button onclick="showToast('Here is some helpful information for you.', 'info')" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                <i class="fas fa-info mr-2"></i>Info Toast
            </button>
        </div>
    </div>

    <!-- Modal Tests -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Enhanced Modals</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Test the enhanced modal system with better animations and styling.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button onclick="testDeleteModal()" 
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                <i class="fas fa-trash mr-2"></i>Test Delete Modal
            </button>
            
            <button onclick="testConfirmModal()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200 btn-hover">
                <i class="fas fa-question mr-2"></i>Test Confirm Modal
            </button>
        </div>
    </div>

    <!-- Search Enhancement Test -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Enhanced Search</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Test the enhanced search functionality with icons and clear buttons.</p>
        
        <form class="max-w-md">
            <div class="relative">
                <input type="text" 
                       name="search" 
                       placeholder="Search members, plans, payments..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white search-input">
            </div>
        </form>
    </div>
</div>

<script>
function testDeleteModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4 shadow-xl modal-enter">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Delete Item</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">This action cannot be undone</p>
                </div>
            </div>
            <div class="mb-6">
                <p class="text-gray-700 dark:text-gray-300">
                    Are you sure you want to delete <strong>Test Item</strong>?
                </p>
                <p class="text-sm text-red-600 dark:text-red-400 mt-2">
                    This will permanently delete all associated data.
                </p>
            </div>
            <div class="flex space-x-3">
                <button onclick="this.closest('.fixed').remove()" 
                        class="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-200">
                    Cancel
                </button>
                <button onclick="showToast('Item deleted successfully!', 'success'); this.closest('.fixed').remove();" 
                        class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-200">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function testConfirmModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4 shadow-xl modal-enter">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-question text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Confirm Action</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Please confirm your choice</p>
                </div>
            </div>
            <div class="mb-6">
                <p class="text-gray-700 dark:text-gray-300">
                    Do you want to proceed with this action?
                </p>
            </div>
            <div class="flex space-x-3">
                <button onclick="this.closest('.fixed').remove()" 
                        class="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-200">
                    Cancel
                </button>
                <button onclick="showToast('Action confirmed!', 'success'); this.closest('.fixed').remove();" 
                        class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
                    <i class="fas fa-check mr-2"></i>Confirm
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// Setup enhanced search for the test input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        const container = searchInput.parentElement;
        container.classList.add('relative');
        
        // Add search icon
        const searchIcon = document.createElement('div');
        searchIcon.className = 'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400';
        searchIcon.innerHTML = '<i class="fas fa-search"></i>';
        container.appendChild(searchIcon);
        
        searchInput.classList.add('pl-10');
        
        searchInput.addEventListener('input', function() {
            const existingClear = container.querySelector('.clear-search');
            if (existingClear) existingClear.remove();
            
            if (this.value.length > 0) {
                const clearButton = document.createElement('button');
                clearButton.type = 'button';
                clearButton.className = 'absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 clear-search';
                clearButton.innerHTML = '<i class="fas fa-times"></i>';
                clearButton.onclick = function() {
                    searchInput.value = '';
                    searchInput.focus();
                };
                container.appendChild(clearButton);
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
