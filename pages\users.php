<?php
/**
 * Users Management Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/ActivityLogger.php';

// Require admin access
Auth::requireAdmin();

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$userId = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $result = handleUserSave();
        if ($result['success']) {
            Session::success($result['message']);
            header('Location: users.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $result = handleUserDelete();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: users.php');
        exit;
    }
}

function handleUserSave() {
    global $db, $action, $userId;

    // Validate input
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $role = $_POST['role'] ?? 'receptionist';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    if (empty($name) || empty($email)) {
        return ['success' => false, 'message' => 'Name and email are required.'];
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Please enter a valid email address.'];
    }

    if ($action === 'add' && empty($password)) {
        return ['success' => false, 'message' => 'Password is required for new users.'];
    }

    try {
        $db->beginTransaction();

        $data = [
            'name' => $name,
            'email' => $email,
            'role' => $role,
            'is_active' => $isActive
        ];

        if ($action === 'add') {
            // Check if email already exists
            $existingUser = $db->fetch("SELECT id FROM users WHERE email = ?", [$email]);
            if ($existingUser) {
                throw new Exception('A user with this email already exists.');
            }

            $data['password'] = password_hash($password, PASSWORD_DEFAULT);
            $newUserId = $db->insert('users', $data);

            // Log activity
            ActivityLogger::log('User Created', 'users', $newUserId, null, $data);

            $message = 'User created successfully.';
        } else {
            // Update existing user
            $oldData = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
            
            // Check if email is being changed and if it conflicts
            if ($oldData['email'] !== $email) {
                $existingUser = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $userId]);
                if ($existingUser) {
                    throw new Exception('A user with this email already exists.');
                }
            }

            // Only update password if provided
            if (!empty($password)) {
                $data['password'] = password_hash($password, PASSWORD_DEFAULT);
            }

            $db->update('users', $data, 'id = ?', [$userId]);

            // Log activity
            ActivityLogger::log('User Updated', 'users', $userId, $oldData, $data);

            $message = 'User updated successfully.';
        }

        $db->commit();
        return ['success' => true, 'message' => $message];

    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to save user: ' . $e->getMessage()];
    }
}

function handleUserDelete() {
    global $db;

    $userId = $_POST['user_id'] ?? null;
    if (!$userId) {
        return ['success' => false, 'message' => 'User ID is required.'];
    }

    // Prevent deleting the current user
    if ($userId == Auth::id()) {
        return ['success' => false, 'message' => 'You cannot delete your own account.'];
    }

    try {
        $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
        if (!$user) {
            return ['success' => false, 'message' => 'User not found.'];
        }

        $db->beginTransaction();

        // Delete user
        $db->delete('users', 'id = ?', [$userId]);

        // Log activity
        ActivityLogger::log('User Deleted', 'users', $userId, $user);

        $db->commit();
        return ['success' => true, 'message' => 'User deleted successfully.'];

    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to delete user: ' . $e->getMessage()];
    }
}

// Get user data for edit
$user = null;
if ($action === 'edit' && $userId) {
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        Session::error('User not found.');
        header('Location: users.php');
        exit;
    }
}

// Get users list with pagination and search
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$roleFilter = $_GET['role'] ?? '';

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR email LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm]);
}

if ($roleFilter) {
    $whereConditions[] = "role = ?";
    $params[] = $roleFilter;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$totalUsers = $db->fetch("SELECT COUNT(*) as count FROM users $whereClause", $params)['count'];
$totalPages = ceil($totalUsers / $limit);

// Get users
$users = $db->fetchAll("
    SELECT *
    FROM users
    $whereClause
    ORDER BY created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

$pageTitle = 'Users';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                <i class="fas fa-user-shield mr-2 text-blue-600"></i>
                Users
            </h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage system users and administrators</p>
        </div>

        <?php if ($action === 'list'): ?>
        <div class="mt-4 sm:mt-0">
            <a href="users.php?action=add"
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add User
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php if ($action === 'list'): ?>
    <!-- Filters and Search -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <input type="text"
                       name="search"
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="Search users..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role</label>
                <select name="role" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Roles</option>
                    <option value="admin" <?= $roleFilter === 'admin' ? 'selected' : '' ?>>Admin</option>
                    <option value="manager" <?= $roleFilter === 'manager' ? 'selected' : '' ?>>Manager</option>
                    <option value="receptionist" <?= $roleFilter === 'receptionist' ? 'selected' : '' ?>>Receptionist</option>
                </select>
            </div>

            <div class="flex items-end space-x-2">
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="users.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- Users Table -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    System Users (<?= number_format($totalUsers) ?>)
                </h3>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($users as $userRow): ?>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white text-sm font-medium">
                                        <?= strtoupper(substr($userRow['name'], 0, 1)) ?>
                                    </span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($userRow['name']) ?>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        <?= htmlspecialchars($userRow['email']) ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <?php
                            $roleColors = [
                                'admin' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                'manager' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                                'receptionist' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            ];
                            $roleColor = $roleColors[$userRow['role']] ?? $roleColors['receptionist'];
                            ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $roleColor ?>">
                                <?= ucfirst($userRow['role']) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <?php if ($userRow['is_active']): ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?= $userRow['last_login'] ? date('M j, Y g:i A', strtotime($userRow['last_login'])) : 'Never' ?>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?= formatDate($userRow['created_at']) ?>
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="users.php?action=edit&id=<?= $userRow['id'] ?>"
                                   class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                   title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if ($userRow['id'] != Auth::id()): ?>
                                <form method="POST" class="inline" onsubmit="return confirmDelete('Are you sure you want to delete this user?')">
                                    <input type="hidden" name="user_id" value="<?= $userRow['id'] ?>">
                                    <input type="hidden" name="action" value="delete">
                                    <?= Auth::csrfField() ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                            title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>

                    <?php if (empty($users)): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-user-shield text-4xl mb-4"></i>
                            <p class="text-lg font-medium">No users found</p>
                            <p class="mt-1">Get started by adding your first user.</p>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Showing <?= ($offset + 1) ?> to <?= min($offset + $limit, $totalUsers) ?> of <?= $totalUsers ?> results
                </div>
                <div class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($roleFilter) ?>"
                           class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg">
                            Previous
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($roleFilter) ?>"
                           class="px-3 py-2 text-sm <?= $i === $page ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600' ?> rounded-lg">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($roleFilter) ?>"
                           class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit User Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <?= $action === 'add' ? 'Add New User' : 'Edit User' ?>
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?php if ($action === 'add'): ?>
                    Create a new system user account with appropriate permissions.
                <?php else: ?>
                    Update user information and permissions.
                <?php endif; ?>
            </p>
        </div>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" class="space-y-6">
            <?= Auth::csrfField() ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           value="<?= htmlspecialchars($user['name'] ?? $_POST['name'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           required>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           value="<?= htmlspecialchars($user['email'] ?? $_POST['email'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           required>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Password <?= $action === 'add' ? '<span class="text-red-500">*</span>' : '(leave blank to keep current)' ?>
                    </label>
                    <input type="password"
                           id="password"
                           name="password"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           <?= $action === 'add' ? 'required' : '' ?>
                           minlength="6">
                    <?php if ($action === 'edit'): ?>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Leave blank to keep current password</p>
                    <?php endif; ?>
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Role <span class="text-red-500">*</span>
                    </label>
                    <select id="role"
                            name="role"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            required>
                        <option value="receptionist" <?= ($user['role'] ?? $_POST['role'] ?? 'receptionist') === 'receptionist' ? 'selected' : '' ?>>Receptionist</option>
                        <option value="manager" <?= ($user['role'] ?? $_POST['role'] ?? '') === 'manager' ? 'selected' : '' ?>>Manager</option>
                        <option value="admin" <?= ($user['role'] ?? $_POST['role'] ?? '') === 'admin' ? 'selected' : '' ?>>Admin</option>
                    </select>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Admin: Full access | Manager: Member & trainer management | Receptionist: Basic operations
                    </p>
                </div>
            </div>

            <div class="flex items-center">
                <input type="checkbox"
                       id="is_active"
                       name="is_active"
                       <?= ($user['is_active'] ?? $_POST['is_active'] ?? true) ? 'checked' : '' ?>
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    Active User
                </label>
                <p class="ml-4 text-xs text-gray-500 dark:text-gray-400">
                    Inactive users cannot log in to the system
                </p>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="users.php"
                   class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>
                    <?= $action === 'add' ? 'Create User' : 'Update User' ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<script>
function confirmDelete(message) {
    return confirm(message);
}
</script>

<?php include '../includes/footer.php'; ?>
