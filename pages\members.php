<?php
/**
 * Members Management Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('members.view');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$memberId = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $result = handleMemberSave();
        if ($result['success']) {
            Session::success($result['message']);
            header('Location: members.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $result = handleMemberDelete();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: members.php');
        exit;
    }
}

function handleMemberSave() {
    global $db, $action, $memberId;

    // Validate input
    $firstName = trim($_POST['first_name'] ?? '');
    $lastName = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $dateOfBirth = $_POST['date_of_birth'] ?? null;
    $gender = $_POST['gender'] ?? null;
    $address = trim($_POST['address'] ?? '');
    $emergencyContact = trim($_POST['emergency_contact'] ?? '');
    $emergencyPhone = trim($_POST['emergency_phone'] ?? '');
    $planId = $_POST['plan_id'] ?? null;
    $trainerId = $_POST['trainer_id'] ?? null;
    $notes = trim($_POST['notes'] ?? '');

    // Payment fields (only for new members)
    $paymentAmount = $_POST['payment_amount'] ?? null;
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $startDate = $_POST['start_date'] ?? date('Y-m-d');
    $transactionId = trim($_POST['transaction_id'] ?? '');

    if (empty($firstName) || empty($lastName) || empty($phone)) {
        return ['success' => false, 'message' => 'First name, last name, and phone are required.'];
    }

    if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Please enter a valid email address.'];
    }

    // Additional validation for new members
    if ($action === 'add') {
        if (!$planId) {
            return ['success' => false, 'message' => 'Please select a membership plan.'];
        }

        if (!$paymentAmount || !is_numeric($paymentAmount) || $paymentAmount <= 0) {
            return ['success' => false, 'message' => 'Please enter a valid payment amount.'];
        }

        if (!$startDate) {
            return ['success' => false, 'message' => 'Please select a membership start date.'];
        }
    }

    try {
        $db->beginTransaction();

        $data = [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $email ?: null,
            'phone' => $phone,
            'date_of_birth' => $dateOfBirth ?: null,
            'gender' => $gender ?: null,
            'address' => $address ?: null,
            'emergency_contact' => $emergencyContact ?: null,
            'emergency_phone' => $emergencyPhone ?: null,
            'plan_id' => $planId ?: null,
            'trainer_id' => $trainerId ?: null,
            'notes' => $notes ?: null
        ];

        if ($action === 'add') {
            // Get plan details for payment calculation
            $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);
            if (!$plan) {
                throw new Exception('Selected plan not found.');
            }

            // Generate unique member ID
            $memberIdPrefix = 'GYM';
            $lastMember = $db->fetch("SELECT member_id FROM members ORDER BY id DESC LIMIT 1");
            if ($lastMember) {
                $lastNumber = (int)substr($lastMember['member_id'], 3);
                $newNumber = $lastNumber + 1;
            } else {
                $newNumber = 1;
            }
            $data['member_id'] = $memberIdPrefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
            $data['status'] = 'active';

            // Calculate membership dates
            $endDate = date('Y-m-d', strtotime($startDate . ' + ' . $plan['duration_months'] . ' months'));

            // Add membership dates to member data
            $data['start_date'] = $startDate;
            $data['end_date'] = $endDate;

            // Insert member
            $newMemberId = $db->insert('members', $data);

            // Create automatic payment record
            $receiptNumber = 'RCP' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

            // Handle processed_by field for local admin vs database users
            $processedBy = null;
            if (Auth::isLocalAdmin()) {
                // For local admins, set processed_by to null since they don't exist in users table
                $processedBy = null;
            } else {
                // For database users, use their actual user ID
                $processedBy = Auth::id();
            }

            $paymentData = [
                'member_id' => $newMemberId,
                'plan_id' => $planId,
                'amount' => $paymentAmount,
                'payment_method' => $paymentMethod,
                'transaction_id' => $transactionId ?: null,
                'receipt_number' => $receiptNumber,
                'payment_date' => $paymentDate,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'notes' => 'Initial membership payment' . (Auth::isLocalAdmin() ? ' (Local Admin)' : ''),
                'processed_by' => $processedBy
            ];

            $paymentId = $db->insert('payments', $paymentData);

            // Log activities
            ActivityLogger::log('Member Created', 'members', $newMemberId, null, $data);
            ActivityLogger::log('Payment Created (Auto)', 'payments', $paymentId, null, $paymentData);

            $message = 'Member added successfully with payment record. Receipt: ' . $receiptNumber;
        } else {
            // Update existing member
            $oldData = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
            $db->update('members', $data, 'id = ?', [$memberId]);

            // Log activity
            ActivityLogger::log('Member Updated', 'members', $memberId, $oldData, $data);

            $message = 'Member updated successfully.';
        }

        $db->commit();
        return ['success' => true, 'message' => $message];

    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to save member: ' . $e->getMessage()];
    }
}

function handleMemberDelete() {
    global $db;

    $memberId = $_POST['member_id'] ?? null;
    if (!$memberId) {
        return ['success' => false, 'message' => 'Member ID is required.'];
    }

    try {
        $member = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
        if (!$member) {
            return ['success' => false, 'message' => 'Member not found.'];
        }

        $db->beginTransaction();

        // Delete member (payments and check-ins will be cascade deleted)
        $db->delete('members', 'id = ?', [$memberId]);

        // Log activity
        ActivityLogger::log('Member Deleted', 'members', $memberId, $member);

        $db->commit();
        return ['success' => true, 'message' => 'Member deleted successfully.'];

    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to delete member: ' . $e->getMessage()];
    }
}

// Get data for forms
$plans = $db->fetchAll("SELECT id, name, price FROM plans WHERE is_active = 1 ORDER BY name");
$trainers = $db->fetchAll("SELECT id, name FROM trainers WHERE is_active = 1 ORDER BY name");

// Get member data for edit
$member = null;
if ($action === 'edit' && $memberId) {
    $member = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
    if (!$member) {
        Session::error('Member not found.');
        header('Location: members.php');
        exit;
    }
}

// Get members list with pagination and search
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$statusFilter = $_GET['status'] ?? '';
$planFilter = $_GET['plan'] ?? '';

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(m.first_name LIKE ? OR m.last_name LIKE ? OR m.email LIKE ? OR m.phone LIKE ? OR m.member_id LIKE ? OR CONCAT(m.first_name, ' ', m.last_name) LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($statusFilter) {
    $whereConditions[] = "m.status = ?";
    $params[] = $statusFilter;
}

if ($planFilter) {
    $whereConditions[] = "m.plan_id = ?";
    $params[] = $planFilter;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count - need to include joins for the WHERE clause to work
$totalMembers = $db->fetch("
    SELECT COUNT(*) as count
    FROM members m
    LEFT JOIN plans p ON m.plan_id = p.id
    LEFT JOIN trainers t ON m.trainer_id = t.id
    $whereClause
", $params)['count'];
$totalPages = ceil($totalMembers / $limit);

// Get members
$members = $db->fetchAll("
    SELECT m.*, p.name as plan_name, t.name as trainer_name,
           CASE
               WHEN m.end_date < CURDATE() THEN 'expired'
               WHEN m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 'expiring'
               ELSE m.status
           END as display_status
    FROM members m
    LEFT JOIN plans p ON m.plan_id = p.id
    LEFT JOIN trainers t ON m.trainer_id = t.id
    $whereClause
    ORDER BY m.created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

$pageTitle = 'Members';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Members</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your gym members</p>
        </div>

        <?php if (Auth::can('members.create')): ?>
        <div class="mt-4 sm:mt-0">
            <a href="members.php?action=add"
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Member
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php if ($action === 'list'): ?>
    <!-- Filters and Search -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <input type="text"
                       name="search"
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="Search members..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Status</option>
                    <option value="active" <?= $statusFilter === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="expired" <?= $statusFilter === 'expired' ? 'selected' : '' ?>>Expired</option>
                    <option value="suspended" <?= $statusFilter === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                    <option value="cancelled" <?= $statusFilter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Plan</label>
                <select name="plan" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Plans</option>
                    <?php foreach ($plans as $plan): ?>
                        <option value="<?= $plan['id'] ?>" <?= $planFilter == $plan['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($plan['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="flex items-end space-x-2">
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="members.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Members Table -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Members (<?= number_format($totalMembers) ?>)
                </h3>
                <div class="flex items-center space-x-2">
                    <button onclick="exportData('members', 'csv')"
                            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Plan</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Trainer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expires</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($members as $memberRow): ?>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
                                    <?php if ($memberRow['avatar']): ?>
                                        <img src="<?= htmlspecialchars($memberRow['avatar']) ?>" alt="" class="w-10 h-10 rounded-full object-cover">
                                    <?php else: ?>
                                        <i class="fas fa-user text-gray-500 dark:text-gray-400"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($memberRow['first_name'] . ' ' . $memberRow['last_name']) ?>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        ID: <?= htmlspecialchars($memberRow['member_id']) ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($memberRow['phone']) ?></div>
                            <?php if ($memberRow['email']): ?>
                                <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($memberRow['email']) ?></div>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-white">
                                <?= htmlspecialchars($memberRow['plan_name'] ?? 'No plan') ?>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-white">
                                <?= htmlspecialchars($memberRow['trainer_name'] ?? 'No trainer') ?>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <?php
                            $statusColors = [
                                'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                'expired' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                'expiring' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                'suspended' => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
                                'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            ];
                            $statusColor = $statusColors[$memberRow['display_status']] ?? $statusColors['active'];
                            ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $statusColor ?>">
                                <?= ucfirst($memberRow['display_status']) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?= $memberRow['end_date'] ? formatDate($memberRow['end_date']) : 'N/A' ?>
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="members.php?action=edit&id=<?= $memberRow['id'] ?>"
                                   class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                   title="Edit Member">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="payments.php?member_id=<?= $memberRow['id'] ?>"
                                   class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                   title="View Payments">
                                    <i class="fas fa-credit-card"></i>
                                </a>
                                <?php if ($memberRow['display_status'] === 'expired' || $memberRow['display_status'] === 'expiring'): ?>
                                <a href="renewals.php?member_id=<?= $memberRow['id'] ?>"
                                   class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                                   title="Renew Membership">
                                    <i class="fas fa-sync-alt"></i>
                                </a>
                                <?php endif; ?>
                                <?php if (Auth::can('members.delete')): ?>
                                <button class="delete-member-btn text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                        data-member-id="<?= $memberRow['id'] ?>"
                                        data-member-name="<?= htmlspecialchars($memberRow['first_name'] . ' ' . $memberRow['last_name']) ?>"
                                        title="Delete Member">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>

                    <?php if (empty($members)): ?>
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p class="text-lg font-medium">No members found</p>
                            <p class="mt-1">Get started by adding your first member.</p>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Showing <?= ($offset + 1) ?> to <?= min($offset + $limit, $totalMembers) ?> of <?= $totalMembers ?> results
                </div>
                <div class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>&plan=<?= urlencode($planFilter) ?>"
                           class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg">
                            Previous
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>&plan=<?= urlencode($planFilter) ?>"
                           class="px-3 py-2 text-sm <?= $i === $page ? 'bg-blue-600 text-white' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600' ?> rounded-lg">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>&plan=<?= urlencode($planFilter) ?>"
                           class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Member Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <?= $action === 'add' ? 'Add New Member' : 'Edit Member' ?>
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?php if ($action === 'add'): ?>
                    Fill in the member details below. A payment record will be automatically created for the new member.
                <?php else: ?>
                    Update the member information.
                <?php endif; ?>
            </p>
        </div>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <?= Auth::csrfField() ?>

            <!-- Personal Information -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Personal Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="first_name"
                               name="first_name"
                               value="<?= htmlspecialchars($member['first_name'] ?? $_POST['first_name'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="last_name"
                               name="last_name"
                               value="<?= htmlspecialchars($member['last_name'] ?? $_POST['last_name'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                        <input type="email"
                               id="email"
                               name="email"
                               value="<?= htmlspecialchars($member['email'] ?? $_POST['email'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phone <span class="text-red-500">*</span>
                        </label>
                        <input type="tel"
                               id="phone"
                               name="phone"
                               value="<?= htmlspecialchars($member['phone'] ?? $_POST['phone'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>

                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date of Birth</label>
                        <input type="date"
                               id="date_of_birth"
                               name="date_of_birth"
                               value="<?= htmlspecialchars($member['date_of_birth'] ?? $_POST['date_of_birth'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Gender</label>
                        <select id="gender"
                                name="gender"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">Select Gender</option>
                            <option value="male" <?= ($member['gender'] ?? $_POST['gender'] ?? '') === 'male' ? 'selected' : '' ?>>Male</option>
                            <option value="female" <?= ($member['gender'] ?? $_POST['gender'] ?? '') === 'female' ? 'selected' : '' ?>>Female</option>
                            <option value="other" <?= ($member['gender'] ?? $_POST['gender'] ?? '') === 'other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</label>
                    <textarea id="address"
                              name="address"
                              rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                              placeholder="Enter full address"><?= htmlspecialchars($member['address'] ?? $_POST['address'] ?? '') ?></textarea>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Emergency Contact</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="emergency_contact" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Contact Name</label>
                        <input type="text"
                               id="emergency_contact"
                               name="emergency_contact"
                               value="<?= htmlspecialchars($member['emergency_contact'] ?? $_POST['emergency_contact'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="emergency_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Contact Phone</label>
                        <input type="tel"
                               id="emergency_phone"
                               name="emergency_phone"
                               value="<?= htmlspecialchars($member['emergency_phone'] ?? $_POST['emergency_phone'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                </div>
            </div>

            <!-- Membership Details -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Membership Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="plan_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Membership Plan <?= $action === 'add' ? '<span class="text-red-500">*</span>' : '' ?>
                        </label>
                        <select id="plan_id"
                                name="plan_id"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                <?= $action === 'add' ? 'required' : '' ?>
                                onchange="updatePaymentAmount()">
                            <option value="">Select Plan</option>
                            <?php foreach ($plans as $plan): ?>
                                <option value="<?= $plan['id'] ?>"
                                        data-price="<?= $plan['price'] ?>"
                                        <?= ($member['plan_id'] ?? $_POST['plan_id'] ?? '') == $plan['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($plan['name']) ?> - <?= formatCurrency($plan['price']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div>
                        <label for="trainer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Assigned Trainer</label>
                        <select id="trainer_id"
                                name="trainer_id"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">No Trainer</option>
                            <?php foreach ($trainers as $trainer): ?>
                                <option value="<?= $trainer['id'] ?>" <?= ($member['trainer_id'] ?? $_POST['trainer_id'] ?? '') == $trainer['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($trainer['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <?php if ($action === 'add'): ?>
            <!-- Payment Information (Only for new members) -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-credit-card mr-2 text-blue-600"></i>
                    Initial Payment Information
                </h4>
                <p class="text-sm text-blue-700 dark:text-blue-300 mb-4">
                    A payment record will be automatically created for this new member. The membership start date determines when the membership period begins.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="payment_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Payment Amount <span class="text-red-500">*</span>
                        </label>
                        <input type="number"
                               id="payment_amount"
                               name="payment_amount"
                               step="0.01"
                               min="0"
                               value="<?= htmlspecialchars($_POST['payment_amount'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="0.00"
                               required>
                    </div>

                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Payment Method <span class="text-red-500">*</span>
                        </label>
                        <select id="payment_method"
                                name="payment_method"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="cash" <?= ($_POST['payment_method'] ?? 'cash') === 'cash' ? 'selected' : '' ?>>Cash</option>
                            <option value="card" <?= ($_POST['payment_method'] ?? '') === 'card' ? 'selected' : '' ?>>Credit/Debit Card</option>
                            <option value="bank_transfer" <?= ($_POST['payment_method'] ?? '') === 'bank_transfer' ? 'selected' : '' ?>>Bank Transfer</option>
                            <option value="check" <?= ($_POST['payment_method'] ?? '') === 'check' ? 'selected' : '' ?>>Check</option>
                            <option value="other" <?= ($_POST['payment_method'] ?? '') === 'other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Membership Start Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="start_date"
                               name="start_date"
                               value="<?= htmlspecialchars($_POST['start_date'] ?? date('Y-m-d')) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>

                    <div>
                        <label for="payment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Payment Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="payment_date"
                               name="payment_date"
                               value="<?= htmlspecialchars($_POST['payment_date'] ?? date('Y-m-d')) ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>
                </div>

                <div class="mt-4">
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Transaction ID / Reference (Optional)
                    </label>
                    <input type="text"
                           id="transaction_id"
                           name="transaction_id"
                           value="<?= htmlspecialchars($_POST['transaction_id'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="Enter transaction ID or reference number">
                </div>
            </div>
            <?php endif; ?>

            <!-- Notes -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea id="notes"
                          name="notes"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Any additional notes about the member..."><?= htmlspecialchars($member['notes'] ?? $_POST['notes'] ?? '') ?></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="members.php"
                   class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>
                    <?= $action === 'add' ? 'Add Member' : 'Update Member' ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<script>
function updatePaymentAmount() {
    const planSelect = document.getElementById('plan_id');
    const paymentAmountInput = document.getElementById('payment_amount');

    if (planSelect && paymentAmountInput) {
        const selectedOption = planSelect.options[planSelect.selectedIndex];
        if (selectedOption && selectedOption.dataset.price) {
            paymentAmountInput.value = selectedOption.dataset.price;
        } else {
            paymentAmountInput.value = '';
        }
    }
}

function syncStartDateWithPaymentDate() {
    const paymentDateInput = document.getElementById('payment_date');
    const startDateInput = document.getElementById('start_date');

    if (paymentDateInput && startDateInput && paymentDateInput.value) {
        // Only sync if start date is empty or same as current payment date
        if (!startDateInput.value || startDateInput.value === paymentDateInput.value) {
            startDateInput.value = paymentDateInput.value;
        }
    }
}

// Auto-update payment amount when page loads if plan is already selected
document.addEventListener('DOMContentLoaded', function() {
    updatePaymentAmount();

    // Add event listener to payment date to sync with start date
    const paymentDateInput = document.getElementById('payment_date');
    if (paymentDateInput) {
        paymentDateInput.addEventListener('change', syncStartDateWithPaymentDate);
    }

    // Enhanced search functionality
    setupSearchEnhancements();

    // Setup delete member event listeners
    setupDeleteMemberListeners();
});

// Setup delete member event listeners
function setupDeleteMemberListeners() {
    const deleteButtons = document.querySelectorAll('.delete-member-btn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const memberId = this.getAttribute('data-member-id');
            const memberName = this.getAttribute('data-member-name');

            deleteMember(memberId, memberName);
        });
    });
}

// Enhanced member deletion with better UI feedback
function deleteMember(memberId, memberName) {
    // Create and show custom modal
    showDeleteModal(memberId, memberName);
}

function showDeleteModal(memberId, memberName) {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.id = 'deleteModal';

    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4 shadow-xl modal-enter">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Delete Member</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">This action cannot be undone</p>
                </div>
            </div>
            <div class="mb-6">
                <p class="text-gray-700 dark:text-gray-300">
                    Are you sure you want to delete <strong>${memberName}</strong>?
                </p>
                <p class="text-sm text-red-600 dark:text-red-400 mt-2">
                    This will permanently delete all member data, payments, and check-in history.
                </p>
            </div>
            <div class="flex space-x-3">
                <button type="button" class="cancel-delete flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-200">
                    Cancel
                </button>
                <button type="button" class="confirm-delete flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-200" data-member-id="${memberId}">
                    <i class="fas fa-trash mr-2"></i>Delete
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners using event delegation
    modal.addEventListener('click', function(e) {
        if (e.target.classList.contains('cancel-delete') || e.target === modal) {
            modal.remove();
        } else if (e.target.classList.contains('confirm-delete') || e.target.closest('.confirm-delete')) {
            const button = e.target.classList.contains('confirm-delete') ? e.target : e.target.closest('.confirm-delete');
            const memberIdToDelete = button.getAttribute('data-member-id');
            processDeleteMember(memberIdToDelete, modal);
        }
    });
}

function processDeleteMember(memberId, modal) {
    const confirmBtn = modal.querySelector('.confirm-delete');

    // Show loading state
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...';
    confirmBtn.disabled = true;

    // Create and submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    form.innerHTML = `
        <input type="hidden" name="member_id" value="${memberId}">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="csrf_token" value="<?= Auth::generateCsrfToken() ?>">
    `;

    document.body.appendChild(form);
    form.submit();
}



// Enhanced search functionality
function setupSearchEnhancements() {
    const searchInput = document.querySelector('input[name="search"]');
    if (!searchInput) return;

    let searchTimeout;

    // Add search icon and clear button
    const searchContainer = searchInput.parentElement;
    searchContainer.classList.add('relative');

    // Add search icon
    const searchIcon = document.createElement('div');
    searchIcon.className = 'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400';
    searchIcon.innerHTML = '<i class="fas fa-search"></i>';
    searchContainer.appendChild(searchIcon);

    // Adjust input padding
    searchInput.classList.add('pl-10');

    // Add clear button when there's text
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);

        // Remove existing clear button
        const existingClear = searchContainer.querySelector('.clear-search');
        if (existingClear) existingClear.remove();

        if (this.value.length > 0) {
            // Add clear button
            const clearButton = document.createElement('button');
            clearButton.type = 'button';
            clearButton.className = 'absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 clear-search';
            clearButton.innerHTML = '<i class="fas fa-times"></i>';
            clearButton.onclick = function() {
                searchInput.value = '';
                searchInput.form.submit();
            };
            searchContainer.appendChild(clearButton);

            // Auto-search after 500ms of no typing
            searchTimeout = setTimeout(() => {
                searchInput.form.submit();
            }, 500);
        }
    });

    // Show search suggestions
    searchInput.addEventListener('focus', function() {
        showSearchSuggestions();
    });
}

function showSearchSuggestions() {
    // You can implement search suggestions here if needed
    // For now, we'll just highlight the search functionality
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput && !searchInput.value) {
        searchInput.placeholder = 'Search by name, email, phone, or member ID...';
    }
}
</script>

<?php include '../includes/footer.php'; ?>